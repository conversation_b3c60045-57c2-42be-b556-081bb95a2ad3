<script lang="ts">
    import { createEventDispatcher, onMount, onDestroy } from 'svelte';
    import type { IDelogoRegion, ISelectionState } from '../../interfaces/video-preview';
    import { generateRegionId, validateDelogoRegion } from '../../interfaces/video-preview';

    // Props
    export let videoWidth: number = 1920;
    export let videoHeight: number = 1080;
    export let previewWidth: number = 800;
    export let previewHeight: number = 450;
    export let regions: IDelogoRegion[] = [];
    export let disabled: boolean = false;
    export let tabId: number | undefined = undefined; // 用于视频帧捕获

    // 事件分发器
    const dispatch = createEventDispatcher();

    // 组件引用
    let canvasElement: HTMLCanvasElement;
    let backgroundCanvas: HTMLCanvasElement; // 用于显示视频帧
    let containerElement: HTMLDivElement;

    // 状态管理
    let selectionState: ISelectionState = {
        isActive: false,
        startX: 0,
        startY: 0,
        currentX: 0,
        currentY: 0,
        regionName: ''
    };

    let isDrawing = false;
    let selectedRegionId: string | null = null;
    let newRegionName = '';

    // 视频帧捕获相关
    let isCapturing = false;
    let captureInterval: number | null = null;
    let lastFrameTime = 0;
    let actualVideoWidth = videoWidth;
    let actualVideoHeight = videoHeight;

    // 计算缩放比例
    $: scaleX = videoWidth / previewWidth;
    $: scaleY = videoHeight / previewHeight;

    onMount(() => {
        if (canvasElement) {
            drawCanvas();
        }
        // 如果提供了 tabId，开始视频帧捕获
        if (tabId) {
            startVideoCapture();
        }
    });

    onDestroy(() => {
        stopVideoCapture();
    });

    /**
     * 开始视频帧捕获
     */
    async function startVideoCapture() {
        if (!tabId || isCapturing) return;

        try {
            isCapturing = true;
            // 每100ms捕获一次帧
            captureInterval = setInterval(captureVideoFrame, 100);
            dispatch('captureStart');
        } catch (error) {
            console.error('启动视频捕获失败:', error);
            dispatch('error', '无法启动视频捕获');
        }
    }

    /**
     * 停止视频帧捕获
     */
    function stopVideoCapture() {
        if (captureInterval) {
            clearInterval(captureInterval);
            captureInterval = null;
        }
        isCapturing = false;
    }

    /**
     * 捕获视频帧
     */
    async function captureVideoFrame() {
        if (!tabId || !backgroundCanvas) return;

        try {
            // 使用chrome.scripting.executeScript直接捕获视频帧
            const result = await chrome.scripting.executeScript({
                target: { tabId: tabId },
                func: (width, height) => {
                    const videoElement = document.querySelector("video") as HTMLVideoElement;
                    if (!videoElement || videoElement.readyState < 2) {
                        return { success: false, error: 'Video not ready' };
                    }

                    try {
                        // 创建canvas来捕获视频帧
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        if (!ctx) {
                            return { success: false, error: 'Cannot get canvas context' };
                        }

                        // 设置canvas尺寸为预览尺寸
                        canvas.width = width;
                        canvas.height = height;

                        // 绘制视频帧到canvas
                        ctx.drawImage(videoElement, 0, 0, width, height);

                        // 转换为base64数据
                        const frameData = canvas.toDataURL('image/jpeg', 0.8);

                        return {
                            success: true,
                            frameData: frameData,
                            videoWidth: videoElement.videoWidth,
                            videoHeight: videoElement.videoHeight
                        };
                    } catch (error) {
                        return {
                            success: false,
                            error: error instanceof Error ? error.message : 'Unknown error'
                        };
                    }
                },
                args: [previewWidth, previewHeight],
                world: 'MAIN'
            });

            const response = result[0]?.result;

            if (response && response.success && response.frameData) {
                const img = new Image();
                img.onload = () => {
                    const ctx = backgroundCanvas.getContext('2d');
                    if (ctx) {
                        // 清空背景画布
                        ctx.clearRect(0, 0, previewWidth, previewHeight);
                        // 绘制视频帧
                        ctx.drawImage(img, 0, 0, previewWidth, previewHeight);

                        // 更新实际视频尺寸
                        if (response.videoWidth && response.videoHeight) {
                            actualVideoWidth = response.videoWidth;
                            actualVideoHeight = response.videoHeight;
                            dispatch('videoSizeUpdate', {
                                width: actualVideoWidth,
                                height: actualVideoHeight
                            });
                        }

                        // 重绘选择层
                        drawCanvas();
                    }
                };
                img.src = response.frameData;
                lastFrameTime = Date.now();
            } else if (response && !response.success) {
                console.warn('视频帧捕获失败:', response.error);
            }
        } catch (error) {
            console.error('捕获视频帧失败:', error);
            // 不要因为单次捕获失败就停止整个捕获过程
        }
    }

    /**
     * 绘制画布内容（选择层）
     */
    function drawCanvas() {
        if (!canvasElement) return;

        const ctx = canvasElement.getContext('2d');
        if (!ctx) return;

        // 清空画布
        ctx.clearRect(0, 0, previewWidth, previewHeight);

        // 绘制现有区域
        regions.forEach(region => {
            drawRegion(ctx, region);
        });

        // 绘制当前选择区域
        if (selectionState.isActive) {
            drawSelectionBox(ctx);
        }
    }

    /**
     * 绘制区域框
     */
    function drawRegion(ctx: CanvasRenderingContext2D, region: IDelogoRegion) {
        // 将像素坐标转换为预览坐标
        const actualScaleX = actualVideoWidth / previewWidth;
        const actualScaleY = actualVideoHeight / previewHeight;
        const x = region.x / actualScaleX;
        const y = region.y / actualScaleY;
        const width = region.width / actualScaleX;
        const height = region.height / actualScaleY;

        // 设置样式
        ctx.strokeStyle = region.enabled ? '#ef4444' : '#6b7280';
        ctx.lineWidth = 2;
        ctx.setLineDash(region.enabled ? [] : [5, 5]);

        // 绘制边框
        ctx.strokeRect(x, y, width, height);

        // 绘制半透明填充
        ctx.fillStyle = region.enabled ? 'rgba(239, 68, 68, 0.1)' : 'rgba(107, 114, 128, 0.1)';
        ctx.fillRect(x, y, width, height);

        // 绘制标签
        ctx.fillStyle = region.enabled ? '#ef4444' : '#6b7280';
        ctx.font = '12px sans-serif';
        ctx.fillText(region.name, x + 4, y + 16);

        // 绘制坐标信息
        const coordText = `${region.x},${region.y} ${region.width}×${region.height}`;
        ctx.fillText(coordText, x + 4, y + height - 4);
    }

    /**
     * 绘制选择框
     */
    function drawSelectionBox(ctx: CanvasRenderingContext2D) {
        const x = Math.min(selectionState.startX, selectionState.currentX);
        const y = Math.min(selectionState.startY, selectionState.currentY);
        const width = Math.abs(selectionState.currentX - selectionState.startX);
        const height = Math.abs(selectionState.currentY - selectionState.startY);

        // 绘制选择框
        ctx.strokeStyle = '#3b82f6';
        ctx.lineWidth = 2;
        ctx.setLineDash([3, 3]);
        ctx.strokeRect(x, y, width, height);

        // 绘制半透明填充
        ctx.fillStyle = 'rgba(59, 130, 246, 0.1)';
        ctx.fillRect(x, y, width, height);

        // 显示尺寸信息
        const pixelWidth = Math.round(width * scaleX);
        const pixelHeight = Math.round(height * scaleY);
        const pixelX = Math.round(x * scaleX);
        const pixelY = Math.round(y * scaleY);

        ctx.fillStyle = '#3b82f6';
        ctx.font = '12px sans-serif';
        ctx.fillText(`${pixelX},${pixelY} ${pixelWidth}×${pixelHeight}`, x + 4, y + 16);
    }

    /**
     * 处理鼠标按下事件
     */
    function handleMouseDown(event: MouseEvent) {
        if (disabled) return;

        const rect = canvasElement.getBoundingClientRect();
        // 考虑canvas可能被CSS缩放的情况
        const scaleX = canvasElement.width / rect.width;
        const scaleY = canvasElement.height / rect.height;
        const x = (event.clientX - rect.left) * scaleX;
        const y = (event.clientY - rect.top) * scaleY;

        // 检查是否点击了现有区域
        const clickedRegion = findRegionAtPoint(x, y);
        if (clickedRegion) {
            selectedRegionId = clickedRegion.id;
            dispatch('regionSelect', clickedRegion);
            return;
        }

        // 开始新的选择
        selectionState = {
            isActive: true,
            startX: x,
            startY: y,
            currentX: x,
            currentY: y,
            regionName: newRegionName || `区域${regions.length + 1}`
        };

        isDrawing = true;
        drawCanvas();
    }

    /**
     * 处理鼠标移动事件
     */
    function handleMouseMove(event: MouseEvent) {
        if (!isDrawing || disabled) return;

        const rect = canvasElement.getBoundingClientRect();
        // 考虑canvas可能被CSS缩放的情况
        const scaleX = canvasElement.width / rect.width;
        const scaleY = canvasElement.height / rect.height;
        selectionState.currentX = (event.clientX - rect.left) * scaleX;
        selectionState.currentY = (event.clientY - rect.top) * scaleY;

        drawCanvas();
    }

    /**
     * 处理鼠标释放事件
     */
    function handleMouseUp(event: MouseEvent) {
        if (!isDrawing || disabled) return;

        isDrawing = false;

        // 计算最终区域
        const x = Math.min(selectionState.startX, selectionState.currentX);
        const y = Math.min(selectionState.startY, selectionState.currentY);
        const width = Math.abs(selectionState.currentX - selectionState.startX);
        const height = Math.abs(selectionState.currentY - selectionState.startY);

        // 检查区域是否足够大
        if (width < 10 || height < 10) {
            selectionState.isActive = false;
            drawCanvas();
            return;
        }

        // 转换为像素坐标（使用实际视频尺寸）
        const actualScaleX = actualVideoWidth / previewWidth;
        const actualScaleY = actualVideoHeight / previewHeight;
        const pixelX = Math.round(x * actualScaleX);
        const pixelY = Math.round(y * actualScaleY);
        const pixelWidth = Math.round(width * actualScaleX);
        const pixelHeight = Math.round(height * actualScaleY);

        // 创建新区域
        const newRegion: IDelogoRegion = {
            id: generateRegionId(),
            name: selectionState.regionName,
            x: pixelX,
            y: pixelY,
            width: pixelWidth,
            height: pixelHeight,
            enabled: true
        };

        // 验证区域（使用实际视频尺寸）
        if (validateDelogoRegion(newRegion, actualVideoWidth, actualVideoHeight)) {
            dispatch('regionAdd', newRegion);
            newRegionName = ''; // 清空输入
        } else {
            dispatch('error', '区域超出视频边界，请重新选择');
        }

        selectionState.isActive = false;
        drawCanvas();
    }

    /**
     * 查找指定点的区域
     */
    function findRegionAtPoint(x: number, y: number): IDelogoRegion | null {
        for (const region of regions) {
            // 将像素坐标转换为预览坐标
            const actualScaleX = actualVideoWidth / previewWidth;
            const actualScaleY = actualVideoHeight / previewHeight;
            const regionX = region.x / actualScaleX;
            const regionY = region.y / actualScaleY;
            const regionWidth = region.width / actualScaleX;
            const regionHeight = region.height / actualScaleY;

            if (x >= regionX && x <= regionX + regionWidth &&
                y >= regionY && y <= regionY + regionHeight) {
                return region;
            }
        }
        return null;
    }

    /**
     * 删除区域
     */
    function deleteRegion(regionId: string) {
        dispatch('regionDelete', regionId);
        if (selectedRegionId === regionId) {
            selectedRegionId = null;
        }
    }

    /**
     * 切换区域启用状态
     */
    function toggleRegion(regionId: string) {
        const region = regions.find(r => r.id === regionId);
        if (region) {
            dispatch('regionToggle', { regionId, enabled: !region.enabled });
        }
    }

    /**
     * 清除所有区域
     */
    function clearAllRegions() {
        dispatch('regionsClear');
        selectedRegionId = null;
    }

    // 响应式更新画布
    $: if (canvasElement && (regions || selectionState)) {
        drawCanvas();
    }
</script>

<div class="region-selector" bind:this={containerElement}>
    <!-- 控制面板 -->
    <div class="controls mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div class="flex items-center gap-3 mb-3">
            <input
                type="text"
                bind:value={newRegionName}
                placeholder="区域名称（可选）"
                class="flex-1 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                {disabled}
            />
            <button
                on:click={clearAllRegions}
                disabled={disabled || regions.length === 0}
                class="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
                清除全部
            </button>
        </div>
        
        <div class="text-xs text-gray-600 dark:text-gray-400">
            <p>• 拖拽鼠标选择要去除logo的区域</p>
            <p>• 点击已有区域可以选中或编辑</p>
            <p>• 视频尺寸: {actualVideoWidth}×{actualVideoHeight} {actualVideoWidth !== videoWidth ? `(检测到: ${actualVideoWidth}×${actualVideoHeight})` : ''}</p>
            {#if tabId}
                <p>• {isCapturing ? '✅ 实时预览已启用' : '⏸️ 实时预览已暂停'}</p>
            {:else}
                <p>• ⚠️ 未连接到视频页面，无法实时预览</p>
            {/if}
        </div>
    </div>

    <!-- 画布容器 -->
    <div class="canvas-container relative border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
        <!-- 背景画布（显示视频帧） -->
        <canvas
            bind:this={backgroundCanvas}
            width={previewWidth}
            height={previewHeight}
            class="block"
        ></canvas>

        <!-- 选择层画布 -->
        <canvas
            bind:this={canvasElement}
            width={previewWidth}
            height={previewHeight}
            class="absolute inset-0 block cursor-crosshair"
            class:cursor-not-allowed={disabled}
            on:mousedown={handleMouseDown}
            on:mousemove={handleMouseMove}
            on:mouseup={handleMouseUp}
        ></canvas>



        {#if disabled}
            <div class="absolute inset-0 bg-gray-500 bg-opacity-50 flex items-center justify-center">
                <span class="text-white font-medium">请先加载视频</span>
            </div>
        {/if}
    </div>

    <!-- 区域列表 -->
    {#if regions.length > 0}
        <div class="regions-list mt-4">
            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                已选择的区域 ({regions.length})
            </h4>
            <div class="space-y-2">
                {#each regions as region (region.id)}
                    <div 
                        class="flex items-center justify-between p-2 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded"
                        class:ring-2={selectedRegionId === region.id}
                        class:ring-blue-500={selectedRegionId === region.id}
                    >
                        <div class="flex items-center gap-3">
                            <input
                                type="checkbox"
                                checked={region.enabled}
                                on:change={() => toggleRegion(region.id)}
                                class="w-4 h-4 text-blue-600 rounded"
                            />
                            <div>
                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                    {region.name}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    x:{region.x} y:{region.y} w:{region.width} h:{region.height}
                                </div>
                            </div>
                        </div>
                        <button
                            on:click={() => deleteRegion(region.id)}
                            class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                            title="删除区域"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                {/each}
            </div>
        </div>
    {/if}
</div>

<style>
    .region-selector {
        user-select: none;
    }
    
    .canvas-container {
        max-width: 100%;
        display: inline-block;
    }

    .canvas-container canvas {
        display: block;
        max-width: 100%;
        height: auto;
    }
</style>
