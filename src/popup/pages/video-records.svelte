<script lang="ts">
	import { onMount } from 'svelte';
	import { storage } from '../stores/storage';
	import { storageDriver } from '../../storage-driver';
	import type { IVideo } from '../../interfaces/video';
	import type { IVideoClip } from '../../interfaces/clip-time';
	import { secondToTimeString } from '../../utils/second-to-time-string';
	import { generateSingleClipCommand } from '../../utils/yt-dlp-generator';
	import CommandPreview from '../components/command-preview.svelte';

	let videos: IVideo[] = [];
	let filteredVideos: IVideo[] = [];
	let searchQuery: string = '';
	let init = false;
	let searchInputRef: HTMLInputElement;

	// 单片段命令预览相关状态
	let showCommandPreview = false;
	let selectedVideo: IVideo | null = null;
	let selectedClip: IVideoClip | null = null;

	// 展开/收起状态管理
	let expandedVideos: Set<string> = new Set();



	$: $storage,
		(async () => {
			if (!init) {
				return;
			}
			await storageDriver.set($storage);
		})();

	// 监听storage.videos变化，自动重新加载视频列表
	$: if (init && $storage.videos) {
		loadAllVideos();
	}

	// 搜索功能
	$: {
		if (searchQuery.trim() === '') {
			filteredVideos = videos;
		} else {
			const query = searchQuery.toLowerCase();
			filteredVideos = videos.filter(video => 
				video.title.toLowerCase().includes(query) ||
				video.clips.some(clip => 
					clip.name?.toLowerCase().includes(query)
				)
			);
		}
	}

	onMount(async () => {
		const res = await storageDriver.get();
		init = true;
		storage.set(res);
		loadAllVideos();
	});

	function loadAllVideos() {
		const storedVideos = $storage.videos;
		const temp: IVideo[] = [];
		for (let key of Object.keys(storedVideos)) {
			const video = storedVideos[key];

			temp.push(video);
		}
		// 按最后修改时间排序（最新的在前）
		videos = temp.sort((a, b) => {
			// 如果没有lastSync，使用视频ID作为fallback
			return 0; // 暂时不排序，后续可以添加时间戳
		});
		filteredVideos = videos;
	}

	async function removeSavedVideo(video: IVideo) {
		const confirmed = confirm(`确定要删除视频"${video.title}"的所有片段吗？此操作不可撤销。`);
		if (!confirmed) return;

		storage.update((prev) => {
			prev.lastSync = new Date().getTime();
			delete prev.videos[video.id];
			return prev;
		});
		loadAllVideos();
	}

	// 跳转到指定片段
	async function jumpToClip(videoId: string, clip: IVideoClip) {
		try {
			const startTime = Math.floor(clip.start);
			const youtubeUrl = `https://www.youtube.com/watch?v=${videoId}&t=${startTime}s`;

			// 检查是否已经有对应的YouTube视频标签页
			const existingTab = await findExistingVideoTab(videoId);

			if (existingTab) {
				// 如果找到现有标签页，切换到该标签页并更新URL到指定时间点
				await chrome.tabs.update(existingTab.id!, {
					url: youtubeUrl,
					active: true
				});
			} else {
				// 如果没有找到，创建新标签页
				await chrome.tabs.create({
					url: youtubeUrl,
					active: true
				});
			}
		} catch (error) {
			console.error('跳转到片段失败:', error);
		}
	}

	// 查找是否已存在对应视频的标签页
	async function findExistingVideoTab(videoId: string): Promise<chrome.tabs.Tab | null> {
		try {
			const tabs = await chrome.tabs.query({});

			// 查找包含相同videoId的YouTube标签页
			for (const tab of tabs) {
				if (tab.url && tab.url.includes('youtube.com/watch')) {
					const urlMatch = tab.url.match(/[?&]v=([^&#]+)/);
					if (urlMatch && urlMatch[1] === videoId) {
						return tab;
					}
				}
			}

			return null;
		} catch (error) {
			console.error('查找现有标签页失败:', error);
			return null;
		}
	}

	// 修改片段功能：智能处理 + 打开delogo界面 + 重命名
	async function editClip(videoId: string, clip: IVideoClip, clipIndex: number) {
		try {
			// 1. 获取当前活动标签页
			const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });

			// 2. 检查当前标签页是否就是目标视频
			const isCurrentTabTargetVideo = currentTab.url &&
				currentTab.url.includes('youtube.com/watch') &&
				currentTab.url.includes(`v=${videoId}`);

			if (isCurrentTabTargetVideo) {
				// 🎯 如果当前就在目标视频页面，直接打开编辑界面，无需跳转
				console.log('当前已在目标视频页面，直接打开编辑界面');
				openClipperForEdit(videoId, clip, clipIndex);
				return;
			}

			// 3. 检查是否有其他标签页打开了目标视频
			const existingTab = await findExistingVideoTab(videoId);

			if (existingTab) {
				// 如果在其他标签页中找到目标视频，切换并更新到指定时间点
				const startTime = Math.floor(clip.start);
				const youtubeUrl = `https://www.youtube.com/watch?v=${videoId}&t=${startTime}s`;

				await chrome.tabs.update(existingTab.id!, {
					url: youtubeUrl,
					active: true
				});

				// 较短的等待时间，因为页面已经存在
				setTimeout(() => {
					openClipperForEdit(videoId, clip, clipIndex);
				}, 500);
			} else {
				// 如果没有找到目标视频的标签页，创建新标签页
				await jumpToClip(videoId, clip);

				// 较长的等待时间，确保新页面加载完成
				setTimeout(() => {
					openClipperForEdit(videoId, clip, clipIndex);
				}, 1500);
			}
		} catch (error) {
			console.error('修改片段失败:', error);
		}
	}

	// 打开clipper页面进行编辑
	function openClipperForEdit(videoId: string, clip: IVideoClip, clipIndex: number) {
		// 存储编辑信息到sessionStorage，供clipper页面使用
		const editInfo = {
			videoId,
			clip,
			clipIndex,
			mode: 'edit'
		};
		sessionStorage.setItem('clipperEditInfo', JSON.stringify(editInfo));

		// 通过自定义事件通知父组件切换到clipper页面
		window.dispatchEvent(new CustomEvent('switchToClipper'));
	}

	// 播放整个视频
	async function playVideo(videoId: string) {
		try {
			const youtubeUrl = `https://www.youtube.com/watch?v=${videoId}`;
			await chrome.tabs.create({
				url: youtubeUrl,
				active: true
			});
		} catch (error) {
			console.error('播放视频失败:', error);
		}
	}

	// 获取YouTube缩略图URL
	function getThumbnailUrl(videoId: string): string {
		return `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`;
	}

	// 格式化片段时长
	function getClipDuration(clip: IVideoClip): string {
		const duration = clip.end - clip.start;
		return secondToTimeString(duration);
	}

	// 删除单个片段
	async function deleteClip(videoId: string, clipIndex: number) {
		const confirmed = confirm('确定要删除这个片段吗？此操作不可撤销。');
		if (!confirmed) return;

		storage.update((prev) => {
			prev.lastSync = new Date().getTime();
			if (prev.videos[videoId] && prev.videos[videoId].clips) {
				prev.videos[videoId].clips.splice(clipIndex, 1);
				// 如果视频没有片段了，自动删除整个视频记录
				if (prev.videos[videoId].clips.length === 0) {
					delete prev.videos[videoId];
				}
			}
			return prev;
		});
		loadAllVideos(); // 重新加载视频列表
	}

	// 全部清空功能
	async function clearAllVideos() {
		const confirmed = confirm('确定要清空所有视频记录吗？此操作不可撤销。');
		if (!confirmed) return;

		const doubleConfirmed = confirm('这将删除所有已保存的视频和片段记录，确定继续吗？');
		if (!doubleConfirmed) return;

		storage.update((prev) => {
			prev.lastSync = new Date().getTime();
			prev.videos = {};
			return prev;
		});

		videos = [];
		filteredVideos = [];
	}

	// 显示单个片段的命令生成
	function showSingleClipCommand(video: IVideo, clip: IVideoClip) {
		selectedVideo = video;
		selectedClip = clip;
		showCommandPreview = true;
	}

	// 批量复制指令 - 直接复制到剪贴板
	async function copyBatchCommand(video: IVideo, event: Event) {
		try {
			const commandOptions = {
				includeMetadata: false,
				includeSubtitles: false,
				writeAutoSubs: false,
				delogoRegions: video.delogoRegions || [],
				videoHeight: video.videoHeight
			};

			// 生成批量复制指令（为每个片段生成单独的命令）
			const commands = video.clips.map(clipItem =>
				generateSingleClipCommand(video.id, clipItem, commandOptions)
			);
			const batchCommand = commands.join('; ');

			await navigator.clipboard.writeText(batchCommand);

			// 显示复制成功提示
			const button = event.target.closest('button');
			if (button) {
				const originalContent = button.innerHTML;
				const originalClasses = button.className;

				// 更改为绿色样式和对号图标
				button.className = button.className.replace(
					'from-blue-400 to-blue-600 hover:from-blue-500 hover:to-blue-700',
					'from-green-400 to-green-600 hover:from-green-500 hover:to-green-700'
				);
				button.innerHTML = '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg>';

				setTimeout(() => {
					button.innerHTML = originalContent;
					button.className = originalClasses;
				}, 2000);
			}
		} catch (error) {
			console.error('批量复制失败:', error);
		}
	}

	// 搜索按钮功能
	function handleSearchClick() {
		if (searchQuery.trim()) {
			// 如果有搜索内容，清空搜索
			searchQuery = '';
		}
		// 聚焦到搜索框
		searchInputRef?.focus();
	}

	// 展开/收起功能
	function toggleVideoExpansion(videoId: string) {
		if (expandedVideos.has(videoId)) {
			expandedVideos.delete(videoId);
		} else {
			expandedVideos.add(videoId);
		}
		expandedVideos = expandedVideos; // 触发响应式更新
	}

	// 检查视频是否展开
	function isVideoExpanded(videoId: string): boolean {
		return expandedVideos.has(videoId);
	}


</script>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	/* 展开/收起按钮样式 */
	.expand-button {
		position: absolute;
		left: -32px;
		top: 50%;
		transform: translateY(-50%);
		width: 24px;
		height: 24px;
		border-radius: 50%;
		background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
		border: 1px solid #d1d5db;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		z-index: 10;
	}

	.dark .expand-button {
		background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
		border-color: #4b5563;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
	}

	.expand-button:hover {
		transform: translateY(-50%) scale(1.1);
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
	}

	.dark .expand-button:hover {
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
	}

	.expand-button svg {
		width: 12px;
		height: 12px;
		color: #6b7280;
		transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.dark .expand-button svg {
		color: #9ca3af;
	}

	.expand-button.expanded svg {
		transform: rotate(90deg);
	}

	/* 片段列表动画 */
	.clips-container {
		overflow: hidden;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.clips-container.collapsed {
		max-height: 0;
		opacity: 0;
		margin-bottom: 0;
	}

	.clips-container.expanded {
		max-height: 2000px;
		opacity: 1;
		margin-bottom: 1rem;
	}

	/* 片段卡片入场动画 */
	.clip-card {
		transform: translateX(20px);
		opacity: 0;
		animation: slideInFromRight 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
	}

	.clip-card:nth-child(1) { animation-delay: 0.1s; }
	.clip-card:nth-child(2) { animation-delay: 0.15s; }
	.clip-card:nth-child(3) { animation-delay: 0.2s; }
	.clip-card:nth-child(4) { animation-delay: 0.25s; }
	.clip-card:nth-child(5) { animation-delay: 0.3s; }
	.clip-card:nth-child(n+6) { animation-delay: 0.35s; }

	@keyframes slideInFromRight {
		to {
			transform: translateX(0);
			opacity: 1;
		}
	}

	/* 删除按钮动画控制 */
	.delete-button {
		transition: opacity 0.2s ease-in-out;
	}

	.clips-container.collapsing .delete-button,
	.clips-container.expanding .delete-button {
		opacity: 0;
	}

	/* 视频卡片容器 */
	.video-card-container {
		position: relative;
	}
</style>

<div class="w-full flex flex-col items-center">
	<!-- 搜索栏 -->
	<div class="w-full max-w-md mb-6 relative px-2">
		<input
			type="text"
			bind:value={searchQuery}
			bind:this={searchInputRef}
			placeholder="搜索视频标题或片段名称..."
			class="w-full px-5 py-3 pr-14 border border-gray-200 dark:border-gray-600 rounded-full
				   bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-700
				   text-gray-800 dark:text-gray-200 shadow-inner
				   focus:outline-none focus:ring-2 focus:ring-purple-500 focus:shadow-lg
				   transition-all duration-200"
		/>
		<button
			on:click={handleSearchClick}
			class="absolute right-3 top-1/2 transform -translate-y-1/2
				   bg-gradient-to-br from-purple-500 to-purple-700 hover:from-purple-600 hover:to-purple-800
				   text-white rounded-full w-9 h-9 flex items-center justify-center
				   shadow-md hover:shadow-lg transition-all duration-200
				   hover:-translate-y-0.5 active:shadow-inner active:transform-none"
			title={searchQuery.trim() ? "清空搜索" : "聚焦搜索框"}
		>
			<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
			</svg>
		</button>
	</div>

	<!-- 视频列表 -->
	{#if filteredVideos.length === 0 && searchQuery.trim() !== ''}
		<div class="w-full text-center py-8">
			<p class="text-gray-500 dark:text-gray-400">未找到匹配的视频记录</p>
		</div>
	{:else if videos.length === 0}
		<div class="w-full text-center py-8">
			<p class="text-gray-500 dark:text-gray-400">暂无视频记录</p>
			<p class="text-sm text-gray-400 dark:text-gray-500 mt-2">在YouTube页面使用剪藏功能来创建视频记录</p>
		</div>
	{:else}
		<div class="w-full space-y-4 px-2">
			{#each filteredVideos as video}
				<div class="video-card-container">
					<!-- 展开/收起按钮 - 只有包含片段的视频才显示 -->
					{#if video.clips.length > 0}
						<button
							class="expand-button {isVideoExpanded(video.id) ? 'expanded' : ''}"
							on:click={() => toggleVideoExpansion(video.id)}
							title={isVideoExpanded(video.id) ? '收起片段列表' : '展开片段列表'}
						>
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
							</svg>
						</button>
					{/if}

					<!-- 主视频卡片 - 轻拟物风格 -->
					<div class="bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700
								text-gray-800 dark:text-white rounded-xl p-4 flex items-center gap-4
								shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200
								border border-gray-200 dark:border-gray-600">
					<!-- 视频缩略图 -->
					<div class="flex-shrink-0 relative">
						<button
							class="w-24 h-18 rounded-xl overflow-hidden hover:opacity-80 transition-all duration-200
								   focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-md hover:shadow-lg
								   transform hover:scale-105"
							on:click={() => playVideo(video.id)}
							title="播放视频: {video.title}"
						>
							<img
								src={getThumbnailUrl(video.id)}
								alt="{video.title} 缩略图"
								class="w-full h-full object-cover"
								on:error={(e) => {
									// 如果缩略图加载失败，显示默认图片
									e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTYiIGhlaWdodD0iNzIiIHZpZXdCb3g9IjAgMCA5NiA3MiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9Ijk2IiBoZWlnaHQ9IjcyIiBmaWxsPSIjNEE1NTY4Ii8+CjxwYXRoIGQ9Ik0zOCAyOEw1OCAzOEwzOCA0OFYyOFoiIGZpbGw9IndoaXRlIi8+Cjx0ZXh0IHg9IjQ4IiB5PSI2MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEwIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VmlkZW88L3RleHQ+Cjwvc3ZnPgo=';
								}}
							/>
							<!-- 播放按钮覆盖层 -->
							<div class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-xl opacity-0 hover:opacity-100 transition-opacity">
								<svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
									<path d="M8 5v14l11-7z"/>
								</svg>
							</div>
						</button>
					</div>

					<!-- 视频信息 -->
					<div class="flex-grow min-w-0">
						<h3 class="font-medium text-sm leading-tight mb-2 line-clamp-2" title={video.title}>
							{video.title}
						</h3>
						<div class="text-xs text-gray-600 dark:text-gray-300 space-y-1">
							<div class="flex items-center gap-4 flex-wrap">
								<span>📹 {video.clips.length} 个片段</span>
								{#if video.clips.length > 0}
									<span>⏱️ 总时长 {getClipDuration({start: Math.min(...video.clips.map(c => c.start)), end: Math.max(...video.clips.map(c => c.end))})}</span>
								{/if}
								{#if video.videoWidth && video.videoHeight}
									<span>📐 {video.videoWidth}×{video.videoHeight}</span>
								{/if}
							</div>
						</div>
					</div>

					<!-- 操作按钮 -->
					<div class="flex-shrink-0">
						<button
							on:click={(event) => copyBatchCommand(video, event)}
							class="bg-gradient-to-br from-blue-400 to-blue-600 hover:from-blue-500 hover:to-blue-700
								   text-white w-12 h-12 rounded-full flex items-center justify-center transition-all duration-200
								   shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:shadow-inner active:transform-none"
							title="批量复制yt-dlp命令"
						>
							<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
								<path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
							</svg>
						</button>
					</div>

					<!-- 片段列表 -->
					{#if video.clips.length > 0}
						<div class="clips-container ml-6 mr-2 space-y-2 {isVideoExpanded(video.id) ? 'expanded' : 'collapsed'}">
						{#each video.clips as clip, clipIndex}
							<div class="clip-card bg-gradient-to-br from-white to-gray-50 dark:from-gray-600 dark:to-gray-700
										rounded-xl px-4 py-3 flex items-center justify-between text-sm
										shadow-md hover:shadow-lg transition-all duration-200
										border border-gray-200 dark:border-gray-500 relative">
								<!-- 右上角悬浮删除按钮 -->
								<button
									class="delete-button absolute -top-1.5 -right-1.5 w-5 h-5 bg-gradient-to-br from-red-400 to-red-600
										   hover:from-red-500 hover:to-red-700 text-white rounded-full
										   flex items-center justify-center text-xs font-bold leading-none
										   transition-all duration-200 shadow-md hover:shadow-lg
										   transform hover:scale-110 active:scale-95 z-10"
									title="删除此片段"
									on:click={() => deleteClip(video.id, clipIndex)}
									style="padding-bottom: 1px;"
								>
									×
								</button>

								<div class="flex items-center gap-3 min-w-0 flex-grow">
									<span class="w-7 h-7 bg-gradient-to-br from-blue-400 to-blue-600 text-white rounded-full
												 flex items-center justify-center text-xs font-bold shadow-md">
										{clipIndex + 1}
									</span>
									<div class="min-w-0 flex-grow">
										<div class="font-medium text-dark dark:text-light truncate flex items-center gap-2">
											{clip.name || `片段${clipIndex + 1}`}
											{#if clip.delogoRegions && clip.delogoRegions.length > 0}
												<span class="inline-flex items-center px-2 py-1 rounded-full text-xs
															 bg-gradient-to-br from-red-100 to-red-200 text-red-800
															 dark:from-red-800 dark:to-red-900 dark:text-red-200
															 shadow-sm border border-red-200 dark:border-red-700">
													Delogo-{clip.delogoRegions.filter(r => r.enabled).length}
												</span>
											{/if}
										</div>
										<div class="text-xs text-gray-600 dark:text-gray-300 flex items-center gap-2">
											<span>{secondToTimeString(clip.start)} - {secondToTimeString(clip.end)}</span>
											<span class="text-gray-500 dark:text-gray-400">({getClipDuration(clip)})</span>
										</div>
									</div>
								</div>
								<div class="flex gap-2">
									<button
										on:click={() => editClip(video.id, clip, clipIndex)}
										class="bg-gradient-to-br from-purple-400 to-purple-600 hover:from-purple-500 hover:to-purple-700
											   text-white px-3 py-1.5 rounded-lg text-xs font-semibold flex-shrink-0
											   transition-all duration-200 shadow-md hover:shadow-lg
											   transform hover:-translate-y-0.5 active:shadow-inner active:transform-none"
										title="修改片段：跳转播放并打开编辑界面"
									>
										✏️ 修改
									</button>

									<button
										class="bg-gradient-to-br from-green-400 to-green-600 hover:from-green-500 hover:to-green-700
											   text-white px-3 py-1.5 rounded-lg text-xs font-semibold flex-shrink-0
											   transition-all duration-200 shadow-md hover:shadow-lg
											   transform hover:-translate-y-0.5 active:shadow-inner active:transform-none"
										title="生成yt-dlp命令"
										on:click={() => showSingleClipCommand(video, clip)}
									>
										⚡ 生成
									</button>
								</div>
							</div>
						{/each}

					</div>
				{/if}
			{/each}
		</div>

		<!-- 全部清空按钮 -->
		{#if filteredVideos.length > 0}
			<div class="w-full flex justify-center mt-8">
				<button
					on:click={clearAllVideos}
					class="bg-gradient-to-br from-red-500 to-red-700 hover:from-red-600 hover:to-red-800
						   text-white px-8 py-3 rounded-xl font-semibold transition-all duration-200
						   shadow-lg hover:shadow-xl transform hover:-translate-y-0.5
						   active:shadow-inner active:transform-none"
					title="清空所有视频记录"
				>
					🗑️ 全部清空
				</button>
			</div>
		{/if}
	{/if}
</div>

<!-- 命令预览模态框 -->
{#if selectedVideo}
	<CommandPreview
		video={selectedVideo}
		clip={selectedClip}
		bind:isOpen={showCommandPreview}
	/>
{/if}


